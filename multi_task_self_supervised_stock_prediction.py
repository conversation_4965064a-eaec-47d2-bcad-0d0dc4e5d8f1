"""
基于多任务自监督学习的股票涨跌预测方法
参考专利：CN113159945A

该方法主要包含以下核心组件：
1. 基于Transformer的股票技术数据序列编码器
2. 多个自监督辅助任务
3. 注意力机制
4. 股票涨跌预测模型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import math
import warnings
warnings.filterwarnings('ignore')


class PositionalEncoding(nn.Module):
    """位置编码模块"""
    def __init__(self, d_model, max_len=5000):
        super(PositionalEncoding, self).__init__()

        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() *
                           (-math.log(10000.0) / d_model))

        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)

        self.register_buffer('pe', pe)

    def forward(self, x):
        # x shape: (batch_size, seq_len, d_model)
        seq_len = x.size(1)
        return x + self.pe[:seq_len, :].unsqueeze(0)


class SimpleAttention(nn.Module):
    """简化的注意力机制"""
    def __init__(self, d_model):
        super(SimpleAttention, self).__init__()
        self.d_model = d_model
        self.attention = nn.MultiheadAttention(d_model, num_heads=4, batch_first=True)

    def forward(self, query, key, value, mask=None):
        attn_output, _ = self.attention(query, key, value, attn_mask=mask)
        return attn_output


class TransformerBlock(nn.Module):
    """Transformer块"""
    def __init__(self, d_model, n_heads, d_ff, dropout=0.1):
        super(TransformerBlock, self).__init__()
        self.attention = SimpleAttention(d_model)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, d_ff),
            nn.ReLU(),
            nn.Linear(d_ff, d_model)
        )
        
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x, mask=None):
        # 自注意力
        attn_output = self.attention(x, x, x, mask)
        x = self.norm1(x + self.dropout(attn_output))
        
        # 前馈网络
        ff_output = self.feed_forward(x)
        x = self.norm2(x + self.dropout(ff_output))
        
        return x


class StockSequenceEncoder(nn.Module):
    """基于Transformer的股票技术数据序列编码器"""
    def __init__(self, input_dim, d_model=256, n_heads=8, n_layers=6, d_ff=1024, dropout=0.1):
        super(StockSequenceEncoder, self).__init__()
        self.d_model = d_model
        
        # 输入投影层
        self.input_projection = nn.Linear(input_dim, d_model)
        
        # 位置编码
        self.pos_encoding = PositionalEncoding(d_model)
        
        # Transformer层
        self.transformer_blocks = nn.ModuleList([
            TransformerBlock(d_model, n_heads, d_ff, dropout)
            for _ in range(n_layers)
        ])
        
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x, mask=None):
        # x shape: (batch_size, seq_len, input_dim)
        x = self.input_projection(x) * math.sqrt(self.d_model)
        x = self.pos_encoding(x)
        x = self.dropout(x)
        
        for transformer in self.transformer_blocks:
            x = transformer(x, mask)
        
        return x


class SelfSupervisedTasks(nn.Module):
    """多个自监督辅助任务"""
    def __init__(self, d_model, seq_len):
        super(SelfSupervisedTasks, self).__init__()
        self.d_model = d_model
        self.seq_len = seq_len
        
        # 任务1: 掩码语言模型 (类似BERT的MLM)
        self.mlm_head = nn.Linear(d_model, d_model)
        
        # 任务2: 下一个时间步预测
        self.next_step_head = nn.Linear(d_model, d_model)
        
        # 任务3: 序列顺序预测
        self.order_head = nn.Linear(d_model * 2, 2)
        
        # 任务4: 对比学习
        self.contrastive_head = nn.Linear(d_model, 128)
    
    def forward(self, encoded_seq, original_seq=None):
        batch_size, seq_len, d_model = encoded_seq.shape

        # 任务1: 掩码语言模型损失 - 简化为自重建任务
        mlm_output = self.mlm_head(encoded_seq)
        mlm_loss = F.mse_loss(mlm_output, encoded_seq)  # 自重建损失

        # 任务2: 下一个时间步预测损失
        next_step_loss = torch.tensor(0.0, device=encoded_seq.device)
        if seq_len > 1:
            next_step_output = self.next_step_head(encoded_seq[:, :-1, :])
            next_step_target = encoded_seq[:, 1:, :]
            next_step_loss = F.mse_loss(next_step_output, next_step_target)

        # 任务3: 序列顺序预测
        # 随机选择两个时间步进行顺序判断
        order_loss = torch.tensor(0.0, device=encoded_seq.device)
        if seq_len > 1:
            idx1 = torch.randint(0, seq_len-1, (batch_size,), device=encoded_seq.device)
            idx2 = torch.randint(0, seq_len-1, (batch_size,), device=encoded_seq.device)

            seq1 = encoded_seq[torch.arange(batch_size), idx1]
            seq2 = encoded_seq[torch.arange(batch_size), idx2]

            order_input = torch.cat([seq1, seq2], dim=-1)
            order_output = self.order_head(order_input)

            # 创建标签 (1 if idx1 < idx2, 0 otherwise)
            order_labels = (idx1 < idx2).long()
            order_loss = F.cross_entropy(order_output, order_labels)

        # 任务4: 对比学习
        contrastive_features = self.contrastive_head(encoded_seq.mean(dim=1))

        return {
            'mlm_loss': mlm_loss,
            'next_step_loss': next_step_loss,
            'order_loss': order_loss,
            'contrastive_features': contrastive_features
        }


class StockPredictionHead(nn.Module):
    """股票涨跌预测头"""
    def __init__(self, d_model, num_classes=3):  # 3类：涨、跌、平
        super(StockPredictionHead, self).__init__()
        self.classifier = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 2, num_classes)
        )
    
    def forward(self, encoded_seq):
        # 使用序列的最后一个时间步进行预测
        last_hidden = encoded_seq[:, -1, :]
        return self.classifier(last_hidden)


class MultiTaskStockPredictor(nn.Module):
    """多任务自监督学习股票预测模型"""
    def __init__(self, input_dim, d_model=256, n_heads=8, n_layers=6, 
                 d_ff=1024, seq_len=60, num_classes=3, dropout=0.1):
        super(MultiTaskStockPredictor, self).__init__()
        
        # 编码器
        self.encoder = StockSequenceEncoder(
            input_dim, d_model, n_heads, n_layers, d_ff, dropout
        )
        
        # 自监督任务
        self.self_supervised_tasks = SelfSupervisedTasks(d_model, seq_len)
        
        # 预测头
        self.prediction_head = StockPredictionHead(d_model, num_classes)
    
    def forward(self, x, original_seq=None, mode='train'):
        # 编码
        encoded_seq = self.encoder(x)
        
        if mode == 'train':
            # 训练模式：计算自监督损失
            ssl_outputs = self.self_supervised_tasks(encoded_seq, original_seq)
            prediction = self.prediction_head(encoded_seq)
            
            return {
                'prediction': prediction,
                'ssl_outputs': ssl_outputs,
                'encoded_seq': encoded_seq
            }
        else:
            # 推理模式：只进行预测
            prediction = self.prediction_head(encoded_seq)
            return prediction


class StockDataset(Dataset):
    """股票数据集"""
    def __init__(self, data, labels, seq_len=60):
        self.data = data
        self.labels = labels
        self.seq_len = seq_len

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        sequence = self.data[idx]  # 数据已经是正确的序列长度
        label = self.labels[idx]
        return torch.FloatTensor(sequence), torch.LongTensor([label])


def create_sample_data(n_samples=10000, seq_len=60, n_features=5):
    """创建示例股票数据"""
    # 模拟股票技术指标数据
    np.random.seed(42)
    
    # 生成基础价格序列
    prices = []
    current_price = 100.0
    
    for _ in range(n_samples + seq_len):
        # 随机游走模拟价格变化
        change = np.random.normal(0, 0.02)
        current_price *= (1 + change)
        prices.append(current_price)
    
    prices = np.array(prices)
    
    # 计算技术指标
    data = []
    labels = []
    
    for i in range(len(prices) - seq_len):
        # 特征：开盘价、最高价、最低价、收盘价、成交量
        price_window = prices[i:i + seq_len + 1]
        
        # 模拟OHLCV数据
        features = []
        for j in range(seq_len):
            open_price = price_window[j]
            close_price = price_window[j + 1]
            high_price = max(open_price, close_price) * (1 + np.random.uniform(0, 0.01))
            low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.01))
            volume = np.random.uniform(1000, 10000)
            
            features.append([open_price, high_price, low_price, close_price, volume])
        
        data.append(features)
        
        # 标签：下一天的涨跌情况
        next_price = price_window[-1]
        current_price = price_window[-2]
        
        if next_price > current_price * 1.01:  # 涨幅超过1%
            label = 2  # 涨
        elif next_price < current_price * 0.99:  # 跌幅超过1%
            label = 0  # 跌
        else:
            label = 1  # 平
        
        labels.append(label)
    
    return np.array(data), np.array(labels)


def train_model():
    """训练模型"""
    # 创建数据
    print("创建示例数据...")
    data, labels = create_sample_data(n_samples=1000, seq_len=30, n_features=5)
    
    # 数据标准化
    scaler = StandardScaler()
    data_reshaped = data.reshape(-1, data.shape[-1])
    data_scaled = scaler.fit_transform(data_reshaped)
    data_scaled = data_scaled.reshape(data.shape)
    
    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(
        data_scaled, labels, test_size=0.2, random_state=42
    )
    
    # 创建数据集和数据加载器
    train_dataset = StockDataset(X_train, y_train, seq_len=30)
    test_dataset = StockDataset(X_test, y_test, seq_len=30)

    train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=8, shuffle=False)
    
    # 创建模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = MultiTaskStockPredictor(
        input_dim=5,  # OHLCV
        d_model=128,
        n_heads=4,
        n_layers=3,
        seq_len=30,
        num_classes=3
    ).to(device)
    
    # 优化器和损失函数
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    criterion = nn.CrossEntropyLoss()
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    print(f"使用设备: {device}")
    
    # 训练循环
    model.train()
    num_epochs = 10
    
    for epoch in range(num_epochs):
        total_loss = 0
        correct = 0
        total = 0
        
        for batch_idx, (data_batch, labels_batch) in enumerate(train_loader):
            data_batch = data_batch.to(device)
            labels_batch = labels_batch.squeeze().to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            outputs = model(data_batch, data_batch, mode='train')
            
            # 主任务损失（股票预测）
            main_loss = criterion(outputs['prediction'], labels_batch)
            
            # 自监督任务损失
            ssl_outputs = outputs['ssl_outputs']
            ssl_loss = (ssl_outputs['mlm_loss'] +
                       ssl_outputs['next_step_loss'] +
                       ssl_outputs['order_loss'])

            # 确保ssl_loss是标量
            if isinstance(ssl_loss, torch.Tensor) and ssl_loss.dim() > 0:
                ssl_loss = ssl_loss.mean()
            
            # 总损失
            total_loss_batch = main_loss + 0.1 * ssl_loss  # 自监督损失权重为0.1
            
            # 反向传播
            total_loss_batch.backward()
            optimizer.step()
            
            total_loss += total_loss_batch.item()
            
            # 计算准确率
            _, predicted = torch.max(outputs['prediction'].data, 1)
            total += labels_batch.size(0)
            correct += (predicted == labels_batch).sum().item()
            
            if batch_idx % 50 == 0:
                print(f'Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}, '
                      f'Loss: {total_loss_batch.item():.4f}, '
                      f'Acc: {100.*correct/total:.2f}%')
        
        avg_loss = total_loss / len(train_loader)
        accuracy = 100. * correct / total
        print(f'Epoch {epoch+1}/{num_epochs} - Loss: {avg_loss:.4f}, Accuracy: {accuracy:.2f}%')
    
    # 测试模型
    model.eval()
    test_correct = 0
    test_total = 0
    
    with torch.no_grad():
        for data_batch, labels_batch in test_loader:
            data_batch = data_batch.to(device)
            labels_batch = labels_batch.squeeze().to(device)
            
            outputs = model(data_batch, mode='inference')
            _, predicted = torch.max(outputs.data, 1)
            
            test_total += labels_batch.size(0)
            test_correct += (predicted == labels_batch).sum().item()
    
    test_accuracy = 100. * test_correct / test_total
    print(f'测试准确率: {test_accuracy:.2f}%')
    
    # 保存模型
    torch.save(model.state_dict(), 'multi_task_stock_predictor.pth')
    print("模型已保存为 'multi_task_stock_predictor.pth'")
    
    return model, scaler


if __name__ == "__main__":
    print("基于多任务自监督学习的股票涨跌预测方法")
    print("=" * 50)
    
    # 训练模型
    model, scaler = train_model()
    
    print("\n模型训练完成！")
    print("该模型实现了以下功能：")
    print("1. 基于Transformer的股票技术数据序列编码器")
    print("2. 多个自监督辅助任务（掩码语言模型、下一步预测、序列顺序预测、对比学习）")
    print("3. 注意力机制用于捕获时间序列中的重要模式")
    print("4. 股票涨跌三分类预测（涨/跌/平）")
