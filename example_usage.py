"""
多任务自监督学习股票预测模型使用示例
"""

import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from multi_task_self_supervised_stock_prediction import (
    MultiTaskStockPredictor, 
    StockDataset, 
    create_sample_data,
    train_model
)
import warnings
warnings.filterwarnings('ignore')

def demo_training():
    """演示模型训练过程"""
    print("=" * 60)
    print("演示：模型训练过程")
    print("=" * 60)
    
    # 训练模型
    model, scaler = train_model()
    
    return model, scaler

def demo_prediction(model, scaler):
    """演示模型预测过程"""
    print("\n" + "=" * 60)
    print("演示：模型预测过程")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.eval()
    
    # 创建测试数据
    test_data, test_labels = create_sample_data(n_samples=100, seq_len=60, n_features=5)
    
    # 数据标准化
    test_data_reshaped = test_data.reshape(-1, test_data.shape[-1])
    test_data_scaled = scaler.transform(test_data_reshaped)
    test_data_scaled = test_data_scaled.reshape(test_data.shape)
    
    # 选择几个样本进行预测
    num_samples = 5
    class_names = ['跌', '平', '涨']
    
    print(f"对{num_samples}个样本进行预测：")
    print("-" * 40)
    
    correct_predictions = 0
    
    for i in range(num_samples):
        # 准备输入数据
        input_data = torch.FloatTensor(test_data_scaled[i:i+1]).to(device)
        true_label = test_labels[i]
        
        # 进行预测
        with torch.no_grad():
            prediction = model(input_data, mode='inference')
            probabilities = torch.softmax(prediction, dim=1)
            predicted_class = torch.argmax(prediction, dim=1).item()
        
        # 显示结果
        print(f"样本 {i+1}:")
        print(f"  真实标签: {class_names[true_label]} ({true_label})")
        print(f"  预测标签: {class_names[predicted_class]} ({predicted_class})")
        print(f"  预测概率: 跌={probabilities[0][0]:.3f}, 平={probabilities[0][1]:.3f}, 涨={probabilities[0][2]:.3f}")
        
        if predicted_class == true_label:
            correct_predictions += 1
            print("  ✓ 预测正确")
        else:
            print("  ✗ 预测错误")
        print()
    
    accuracy = correct_predictions / num_samples * 100
    print(f"样本预测准确率: {accuracy:.1f}%")

def demo_feature_analysis(model):
    """演示特征分析"""
    print("\n" + "=" * 60)
    print("演示：模型特征分析")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.eval()
    
    # 创建示例数据
    sample_data = torch.randn(1, 60, 5).to(device)
    
    # 获取编码器输出
    with torch.no_grad():
        encoded_features = model.encoder(sample_data)
    
    print(f"输入数据形状: {sample_data.shape}")
    print(f"编码特征形状: {encoded_features.shape}")
    print(f"特征维度: {encoded_features.shape[-1]}")
    
    # 分析注意力权重（简化版）
    print("\n注意力机制分析:")
    print("- 模型使用多头注意力机制")
    print("- 自动学习时间序列中的重要模式")
    print("- 动态分配权重给不同时间步")

def demo_real_time_prediction():
    """演示实时预测功能"""
    print("\n" + "=" * 60)
    print("演示：实时预测功能")
    print("=" * 60)
    
    class StreamingPredictor:
        def __init__(self, model, scaler, seq_len=60):
            self.model = model
            self.scaler = scaler
            self.seq_len = seq_len
            self.buffer = []
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            self.model.eval()
        
        def add_data_point(self, data_point):
            """添加新的数据点"""
            self.buffer.append(data_point)
            if len(self.buffer) > self.seq_len:
                self.buffer.pop(0)  # 保持固定长度
        
        def predict(self):
            """进行预测"""
            if len(self.buffer) < self.seq_len:
                return None, "数据不足，需要至少{}个数据点".format(self.seq_len)
            
            # 准备数据
            sequence = np.array(self.buffer).reshape(1, -1, 5)
            sequence_scaled = self.scaler.transform(sequence.reshape(-1, 5)).reshape(sequence.shape)
            input_tensor = torch.FloatTensor(sequence_scaled).to(self.device)
            
            # 预测
            with torch.no_grad():
                prediction = self.model(input_tensor, mode='inference')
                probabilities = torch.softmax(prediction, dim=1)
                predicted_class = torch.argmax(prediction, dim=1).item()
            
            class_names = ['跌', '平', '涨']
            return predicted_class, {
                'class': class_names[predicted_class],
                'probabilities': probabilities[0].cpu().numpy()
            }
    
    # 加载模型（这里使用刚训练的模型）
    try:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = MultiTaskStockPredictor(input_dim=5, d_model=256, n_heads=8, n_layers=6, seq_len=60, num_classes=3)
        model.load_state_dict(torch.load('multi_task_stock_predictor.pth'))
        model.to(device)
        
        # 创建标准化器（实际使用中应该加载保存的scaler）
        scaler = StandardScaler()
        dummy_data = np.random.randn(1000, 5)
        scaler.fit(dummy_data)
        
        # 创建流式预测器
        predictor = StreamingPredictor(model, scaler)
        
        print("模拟实时数据流预测:")
        print("-" * 30)
        
        # 模拟实时数据
        for i in range(65):  # 添加65个数据点
            # 模拟OHLCV数据
            data_point = [
                100 + np.random.normal(0, 1),  # 开盘价
                102 + np.random.normal(0, 1),  # 最高价
                98 + np.random.normal(0, 1),   # 最低价
                101 + np.random.normal(0, 1),  # 收盘价
                np.random.uniform(1000, 5000)  # 成交量
            ]
            
            predictor.add_data_point(data_point)
            
            # 每5个数据点进行一次预测
            if (i + 1) % 5 == 0:
                pred_class, pred_info = predictor.predict()
                if pred_class is not None:
                    probs = pred_info['probabilities']
                    print(f"时间步 {i+1}: 预测={pred_info['class']}, "
                          f"概率=[跌:{probs[0]:.3f}, 平:{probs[1]:.3f}, 涨:{probs[2]:.3f}]")
                else:
                    print(f"时间步 {i+1}: {pred_info}")
        
    except FileNotFoundError:
        print("未找到预训练模型文件，请先运行训练过程")

def demo_model_interpretation():
    """演示模型解释性分析"""
    print("\n" + "=" * 60)
    print("演示：模型解释性分析")
    print("=" * 60)
    
    print("模型架构解释:")
    print("1. 输入层: 接收OHLCV等技术指标")
    print("2. 位置编码: 保持时间序列顺序信息")
    print("3. Transformer编码器: 提取深层时序特征")
    print("4. 多任务自监督学习:")
    print("   - 掩码语言模型: 学习特征重构")
    print("   - 下一步预测: 学习时序演化规律")
    print("   - 顺序预测: 学习时间关系")
    print("   - 对比学习: 学习样本相似性")
    print("5. 预测头: 输出涨跌分类结果")
    
    print("\n自监督学习的优势:")
    print("- 无需大量标注数据")
    print("- 自动发现数据中的模式")
    print("- 提高模型泛化能力")
    print("- 学习更鲁棒的特征表示")

def main():
    """主函数"""
    print("基于多任务自监督学习的股票涨跌预测方法 - 使用示例")
    print("参考专利：CN113159945A")
    
    # 1. 演示训练过程
    model, scaler = demo_training()
    
    # 2. 演示预测过程
    demo_prediction(model, scaler)
    
    # 3. 演示特征分析
    demo_feature_analysis(model)
    
    # 4. 演示实时预测
    demo_real_time_prediction()
    
    # 5. 演示模型解释
    demo_model_interpretation()
    
    print("\n" + "=" * 60)
    print("演示完成！")
    print("=" * 60)
    print("\n使用建议:")
    print("1. 使用真实的股票数据替换示例数据")
    print("2. 根据具体需求调整模型超参数")
    print("3. 增加更多技术指标作为输入特征")
    print("4. 定期重新训练模型以适应市场变化")
    print("5. 结合其他分析方法进行综合判断")
    print("\n注意：本模型仅用于学习研究，不构成投资建议！")

if __name__ == "__main__":
    main()
