"""
简化版多任务自监督学习股票预测模型使用示例
"""

import torch
import numpy as np
from simple_stock_prediction import (
    SimpleStockPredictor, 
    create_sample_data,
    train_model
)
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

def demo_training():
    """演示模型训练过程"""
    print("=" * 60)
    print("演示：简化版模型训练过程")
    print("=" * 60)
    
    # 训练模型
    model, scaler = train_model()
    
    return model, scaler

def demo_prediction(model, scaler):
    """演示模型预测过程"""
    print("\n" + "=" * 60)
    print("演示：模型预测过程")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.eval()
    
    # 创建测试数据
    test_data, test_labels = create_sample_data(n_samples=50, seq_len=30)
    
    # 数据标准化
    test_data_reshaped = test_data.reshape(-1, test_data.shape[-1])
    test_data_scaled = scaler.transform(test_data_reshaped)
    test_data_scaled = test_data_scaled.reshape(test_data.shape)
    
    # 选择几个样本进行预测
    num_samples = 5
    class_names = ['跌', '平', '涨']
    
    print(f"对{num_samples}个样本进行预测：")
    print("-" * 40)
    
    correct_predictions = 0
    
    for i in range(num_samples):
        # 准备输入数据
        input_data = torch.FloatTensor(test_data_scaled[i:i+1]).to(device)
        true_label = test_labels[i]
        
        # 进行预测
        with torch.no_grad():
            prediction = model(input_data, mode='inference')
            probabilities = torch.softmax(prediction, dim=1)
            predicted_class = torch.argmax(prediction, dim=1).item()
        
        # 显示结果
        print(f"样本 {i+1}:")
        print(f"  真实标签: {class_names[true_label]} ({true_label})")
        print(f"  预测标签: {class_names[predicted_class]} ({predicted_class})")
        print(f"  预测概率: 跌={probabilities[0][0]:.3f}, 平={probabilities[0][1]:.3f}, 涨={probabilities[0][2]:.3f}")
        
        if predicted_class == true_label:
            correct_predictions += 1
            print("  ✓ 预测正确")
        else:
            print("  ✗ 预测错误")
        print()
    
    accuracy = correct_predictions / num_samples * 100
    print(f"样本预测准确率: {accuracy:.1f}%")

def demo_model_analysis():
    """演示模型分析"""
    print("\n" + "=" * 60)
    print("演示：模型架构分析")
    print("=" * 60)
    
    # 创建模型实例
    model = SimpleStockPredictor(input_dim=5, hidden_dim=64, num_classes=3)
    
    print("模型架构组件:")
    print("1. 输入投影层: 将5维OHLCV数据投影到64维隐藏空间")
    print("2. LSTM编码器: 2层LSTM，捕获时序依赖关系")
    print("3. 自注意力层: 4头注意力机制，学习重要时间步")
    print("4. 自监督任务:")
    print("   - 序列重构: 学习数据的内在表示")
    print("   - 下一步预测: 学习时序演化规律")
    print("   - 对比学习: 学习样本间的相似性")
    print("5. 预测头: 三分类输出（涨/跌/平）")
    
    # 计算参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"\n模型参数统计:")
    print(f"总参数数量: {total_params:,}")
    print(f"可训练参数: {trainable_params:,}")
    
    # 显示各组件参数
    print(f"\n各组件参数分布:")
    for name, module in model.named_children():
        module_params = sum(p.numel() for p in module.parameters())
        print(f"{name}: {module_params:,} 参数")

def demo_real_time_prediction():
    """演示实时预测功能"""
    print("\n" + "=" * 60)
    print("演示：实时预测功能")
    print("=" * 60)
    
    class StreamingPredictor:
        def __init__(self, model, scaler, seq_len=30):
            self.model = model
            self.scaler = scaler
            self.seq_len = seq_len
            self.buffer = []
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            self.model.eval()
        
        def add_data_point(self, data_point):
            """添加新的数据点"""
            self.buffer.append(data_point)
            if len(self.buffer) > self.seq_len:
                self.buffer.pop(0)  # 保持固定长度
        
        def predict(self):
            """进行预测"""
            if len(self.buffer) < self.seq_len:
                return None, f"数据不足，需要至少{self.seq_len}个数据点"
            
            # 准备数据
            sequence = np.array(self.buffer).reshape(1, self.seq_len, 5)
            sequence_scaled = self.scaler.transform(sequence.reshape(-1, 5)).reshape(sequence.shape)
            input_tensor = torch.FloatTensor(sequence_scaled).to(self.device)
            
            # 预测
            with torch.no_grad():
                prediction = self.model(input_tensor, mode='inference')
                probabilities = torch.softmax(prediction, dim=1)
                predicted_class = torch.argmax(prediction, dim=1).item()
            
            class_names = ['跌', '平', '涨']
            return predicted_class, {
                'class': class_names[predicted_class],
                'probabilities': probabilities[0].cpu().numpy()
            }
    
    # 尝试加载模型
    try:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = SimpleStockPredictor(input_dim=5, hidden_dim=64, num_classes=3)
        model.load_state_dict(torch.load('simple_stock_predictor.pth'))
        model.to(device)
        
        # 创建标准化器（实际使用中应该加载保存的scaler）
        scaler = StandardScaler()
        dummy_data = np.random.randn(1000, 5)
        scaler.fit(dummy_data)
        
        # 创建流式预测器
        predictor = StreamingPredictor(model, scaler)
        
        print("模拟实时数据流预测:")
        print("-" * 30)
        
        # 模拟实时数据
        for i in range(35):  # 添加35个数据点
            # 模拟OHLCV数据
            data_point = [
                100 + np.random.normal(0, 1),  # 开盘价
                102 + np.random.normal(0, 1),  # 最高价
                98 + np.random.normal(0, 1),   # 最低价
                101 + np.random.normal(0, 1),  # 收盘价
                np.random.uniform(1000, 5000)  # 成交量
            ]
            
            predictor.add_data_point(data_point)
            
            # 每5个数据点进行一次预测
            if (i + 1) % 5 == 0:
                pred_class, pred_info = predictor.predict()
                if pred_class is not None:
                    probs = pred_info['probabilities']
                    print(f"时间步 {i+1}: 预测={pred_info['class']}, "
                          f"概率=[跌:{probs[0]:.3f}, 平:{probs[1]:.3f}, 涨:{probs[2]:.3f}]")
                else:
                    print(f"时间步 {i+1}: {pred_info}")
        
    except FileNotFoundError:
        print("未找到预训练模型文件，请先运行训练过程")

def demo_model_interpretation():
    """演示模型解释性分析"""
    print("\n" + "=" * 60)
    print("演示：模型解释性分析")
    print("=" * 60)
    
    print("多任务自监督学习的核心思想:")
    print("1. 主任务: 股票涨跌预测")
    print("2. 辅助任务: 自监督学习任务")
    print("   - 帮助模型学习更好的数据表示")
    print("   - 提高模型的泛化能力")
    print("   - 减少对标注数据的依赖")
    
    print("\n与传统方法的对比:")
    print("传统方法:")
    print("- 仅使用标注数据训练")
    print("- 容易过拟合")
    print("- 需要大量标注样本")
    
    print("\n多任务自监督方法:")
    print("- 利用数据的内在结构")
    print("- 学习更鲁棒的特征")
    print("- 提高小样本学习能力")
    
    print("\n实际应用建议:")
    print("1. 收集高质量的历史股票数据")
    print("2. 增加更多技术指标特征")
    print("3. 根据市场特点调整模型参数")
    print("4. 定期重新训练以适应市场变化")
    print("5. 结合基本面分析进行综合判断")

def main():
    """主函数"""
    print("简化版多任务自监督学习股票预测模型 - 使用示例")
    print("基于专利CN113159945A的核心思想")
    
    # 1. 演示训练过程
    model, scaler = demo_training()
    
    # 2. 演示预测过程
    demo_prediction(model, scaler)
    
    # 3. 演示模型分析
    demo_model_analysis()
    
    # 4. 演示实时预测
    demo_real_time_prediction()
    
    # 5. 演示模型解释
    demo_model_interpretation()
    
    print("\n" + "=" * 60)
    print("演示完成！")
    print("=" * 60)
    print("\n核心特点:")
    print("✓ 基于LSTM + 自注意力的序列编码")
    print("✓ 多任务自监督学习框架")
    print("✓ 端到端训练优化")
    print("✓ 支持实时预测")
    print("✓ 模型轻量化设计")
    
    print("\n使用建议:")
    print("1. 使用真实股票数据进行训练")
    print("2. 根据数据特点调整超参数")
    print("3. 增加更多技术指标特征")
    print("4. 定期重新训练模型")
    print("5. 结合风险管理策略使用")
    print("\n⚠️  注意：本模型仅用于学习研究，不构成投资建议！")

if __name__ == "__main__":
    main()
