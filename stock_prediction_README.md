# 基于多任务自监督学习的股票涨跌预测方法

## 项目简介

本项目实现了专利CN113159945A中描述的"一种基于多任务自监督学习的股票涨跌预测方法"。该方法结合了Transformer架构、注意力机制和多个自监督学习任务，能够自动从原始金融信号中学习有价值的特征，并用于股票涨跌预测。

## 核心技术特点

### 1. 基于Transformer的序列编码器
- 使用多头注意力机制捕获时间序列中的长期依赖关系
- 位置编码保持时间序列的顺序信息
- 多层Transformer块提取深层特征

### 2. 多任务自监督学习
- **掩码语言模型(MLM)**: 类似BERT的掩码预测任务
- **下一个时间步预测**: 预测序列的下一个状态
- **序列顺序预测**: 判断两个时间步的先后顺序
- **对比学习**: 学习相似和不相似样本的表示

### 3. 注意力机制
- 多头注意力机制自动学习重要的时间步和特征
- 动态权重分配，突出关键信息

## 快速开始

### 1. 训练模型

```python
from multi_task_self_supervised_stock_prediction import train_model

# 训练模型
model, scaler = train_model()
```

### 2. 使用预训练模型进行预测

```python
import torch
from multi_task_self_supervised_stock_prediction import MultiTaskStockPredictor
import numpy as np

# 加载模型
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model = MultiTaskStockPredictor(input_dim=5, d_model=256, n_heads=8, n_layers=6, seq_len=60, num_classes=3)
model.load_state_dict(torch.load('multi_task_stock_predictor.pth'))
model.to(device)
model.eval()

# 准备输入数据 (batch_size, seq_len, features)
# 特征顺序：开盘价、最高价、最低价、收盘价、成交量
input_data = torch.randn(1, 60, 5).to(device)  # 示例数据

# 进行预测
with torch.no_grad():
    prediction = model(input_data, mode='inference')
    predicted_class = torch.argmax(prediction, dim=1)
    
    # 0: 跌, 1: 平, 2: 涨
    class_names = ['跌', '平', '涨']
    print(f"预测结果: {class_names[predicted_class.item()]}")
```

## 模型架构详解

### 1. 输入层
- 接受股票技术指标序列（OHLCV等）
- 支持自定义特征维度和序列长度

### 2. 编码器层
```python
StockSequenceEncoder(
    input_dim=5,      # 输入特征维度
    d_model=256,      # 模型隐藏维度
    n_heads=8,        # 注意力头数
    n_layers=6,       # Transformer层数
    d_ff=1024,        # 前馈网络维度
    dropout=0.1       # Dropout率
)
```

### 3. 自监督任务层
- **MLM任务**: 重构被掩码的输入特征
- **下一步预测**: 预测序列的下一个时间步
- **顺序预测**: 判断时间步的相对顺序
- **对比学习**: 学习样本间的相似性

### 4. 预测层
- 三分类输出：涨(2)、跌(0)、平(1)
- 可扩展为回归任务预测具体涨跌幅度

## 训练策略

### 1. 损失函数
```python
total_loss = main_loss + α * ssl_loss
```
其中：
- `main_loss`: 主任务分类损失
- `ssl_loss`: 自监督任务损失的加权和
- `α`: 自监督损失权重（默认0.1）

### 2. 优化器
- 使用Adam优化器
- 学习率：0.001
- 支持学习率调度

### 3. 数据预处理
- StandardScaler标准化
- 滑动窗口构建序列
- 标签平滑（可选）

## 性能优化建议

### 1. 数据方面
- 增加更多技术指标特征
- 使用更长的历史序列
- 数据增强技术

### 2. 模型方面
- 调整模型超参数
- 增加正则化技术
- 使用预训练权重

### 3. 训练方面
- 使用更大的批次大小
- 实现梯度累积
- 多GPU并行训练

## 扩展功能

### 1. 多股票预测
```python
# 支持同时预测多只股票
class MultiStockPredictor(MultiTaskStockPredictor):
    def __init__(self, num_stocks, **kwargs):
        super().__init__(**kwargs)
        self.stock_embedding = nn.Embedding(num_stocks, self.d_model)
```

### 2. 回归预测
```python
# 预测具体涨跌幅度
class RegressionHead(nn.Module):
    def __init__(self, d_model):
        super().__init__()
        self.regressor = nn.Linear(d_model, 1)
    
    def forward(self, x):
        return self.regressor(x[:, -1, :])
```

### 3. 实时预测
```python
# 支持流式数据预测
class StreamingPredictor:
    def __init__(self, model, scaler, seq_len=60):
        self.model = model
        self.scaler = scaler
        self.seq_len = seq_len
        self.buffer = []
    
    def update_and_predict(self, new_data):
        self.buffer.append(new_data)
        if len(self.buffer) >= self.seq_len:
            sequence = np.array(self.buffer[-self.seq_len:])
            # 预测逻辑
            return self.predict(sequence)
```

## 注意事项

1. **数据质量**: 确保输入数据的质量和一致性
2. **过拟合**: 注意监控验证集性能，避免过拟合
3. **市场变化**: 定期重新训练模型以适应市场变化
4. **风险管理**: 模型预测仅供参考，不构成投资建议

## 参考文献

- 专利号：CN113159945A
- 标题：一种基于多任务自监督学习的股票涨跌预测方法
- 核心技术：Transformer + 多任务自监督学习 + 注意力机制
